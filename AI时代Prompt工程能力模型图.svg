<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" 
     viewBox="0 0 1600 1600" 
     width="1600" height="1600"
     style="max-width: 100%; height: auto; background: #f8fafc;">
  
  <defs>
    <!-- 渐变定义 -->
    <radialGradient id="centerGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1E3A8A"/>
      <stop offset="100%" style="stop-color:#3B82F6"/>
    </radialGradient>
    
    <radialGradient id="techGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#DBEAFE"/>
      <stop offset="100%" style="stop-color:#3B82F6"/>
    </radialGradient>
    
    <radialGradient id="designGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#D1FAE5"/>
      <stop offset="100%" style="stop-color:#10B981"/>
    </radialGradient>
    
    <radialGradient id="domainGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#EDE9FE"/>
      <stop offset="100%" style="stop-color:#8B5CF6"/>
    </radialGradient>
    
    <radialGradient id="innovationGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FEF3C7"/>
      <stop offset="100%" style="stop-color:#F59E0B"/>
    </radialGradient>
    
    <radialGradient id="analysisGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FEE2E2"/>
      <stop offset="100%" style="stop-color:#EF4444"/>
    </radialGradient>
    
    <radialGradient id="collabGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#CFFAFE"/>
      <stop offset="100%" style="stop-color:#06B6D4"/>
    </radialGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 背景圆环 -->
  <circle cx="800" cy="800" r="600" fill="none" stroke="#E2E8F0" stroke-width="1" opacity="0.3"/>
  <circle cx="800" cy="800" r="400" fill="none" stroke="#E2E8F0" stroke-width="1" opacity="0.2"/>
  
  <!-- 标题 -->
  <text x="800" y="80" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #1E293B;">
    AI时代Prompt提问工程能力模型
  </text>
  
  <text x="800" y="120" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 16px; fill: #64748B;">
    AI Era Prompt Engineering Competency Model
  </text>
  
  <!-- 六边形连接线 -->
  <g stroke="#94A3B8" stroke-width="2" opacity="0.4">
    <line x1="1200" y1="800" x2="1000" y2="453"/>
    <line x1="1000" y1="453" x2="600" y2="453"/>
    <line x1="600" y1="453" x2="400" y2="800"/>
    <line x1="400" y1="800" x2="600" y2="1147"/>
    <line x1="600" y1="1147" x2="1000" y2="1147"/>
    <line x1="1000" y1="1147" x2="1200" y2="800"/>
  </g>
  
  <!-- 中心到各维度连接线 -->
  <g stroke="#64748B" stroke-width="2" opacity="0.6">
    <line x1="800" y1="800" x2="1200" y2="800"/>
    <line x1="800" y1="800" x2="1000" y2="453"/>
    <line x1="800" y1="800" x2="600" y2="453"/>
    <line x1="800" y1="800" x2="400" y2="800"/>
    <line x1="800" y1="800" x2="600" y2="1147"/>
    <line x1="800" y1="800" x2="1000" y2="1147"/>
  </g>
  
  <!-- 中心主题 -->
  <circle cx="800" cy="800" r="100" fill="url(#centerGrad)" 
          stroke="#1E3A8A" stroke-width="3" filter="url(#shadow)"/>
  <text x="800" y="790" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: white;">
    Prompt工程师
  </text>
  <text x="800" y="810" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 12px; fill: #DBEAFE;">
    核心能力模型
  </text>
  
  <!-- 1. 技术基础能力 -->
  <circle cx="1200" cy="800" r="70" fill="url(#techGrad)" 
          stroke="#1E40AF" stroke-width="3" filter="url(#shadow)"/>
  <text x="1200" y="795" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: white;">
    技术基础
  </text>
  <text x="1200" y="810" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 10px; fill: #DBEAFE;">
    Technical
  </text>
  
  <!-- 技术基础子能力 -->
  <circle cx="1320" cy="720" r="25" fill="white" stroke="#3B82F6" stroke-width="2"/>
  <text x="1320" y="725" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #1E40AF;">AI模型</text>
  
  <circle cx="1350" cy="800" r="25" fill="white" stroke="#3B82F6" stroke-width="2"/>
  <text x="1350" y="805" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #1E40AF;">API</text>
  
  <circle cx="1320" cy="880" r="25" fill="white" stroke="#3B82F6" stroke-width="2"/>
  <text x="1320" y="885" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #1E40AF;">编程</text>
  
  <circle cx="1270" cy="920" r="25" fill="white" stroke="#3B82F6" stroke-width="2"/>
  <text x="1270" y="925" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #1E40AF;">工具</text>
  
  <!-- 连接线 -->
  <g stroke="#3B82F6" stroke-width="1" opacity="0.6">
    <line x1="1200" y1="800" x2="1320" y2="720"/>
    <line x1="1200" y1="800" x2="1350" y2="800"/>
    <line x1="1200" y1="800" x2="1320" y2="880"/>
    <line x1="1200" y1="800" x2="1270" y2="920"/>
  </g>
  
  <!-- 2. 提问设计能力 -->
  <circle cx="1000" cy="453" r="70" fill="url(#designGrad)" 
          stroke="#047857" stroke-width="3" filter="url(#shadow)"/>
  <text x="1000" y="448" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: white;">
    提问设计
  </text>
  <text x="1000" y="463" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 10px; fill: #D1FAE5;">
    Question
  </text>
  
  <!-- 提问设计子能力 -->
  <circle cx="1100" cy="320" r="25" fill="white" stroke="#10B981" stroke-width="2"/>
  <text x="1100" y="325" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #047857;">结构化</text>
  
  <circle cx="1130" cy="400" r="25" fill="white" stroke="#10B981" stroke-width="2"/>
  <text x="1130" y="405" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #047857;">逻辑</text>
  
  <circle cx="1130" cy="500" r="25" fill="white" stroke="#10B981" stroke-width="2"/>
  <text x="1130" y="505" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #047857;">语言</text>
  
  <circle cx="1070" cy="550" r="25" fill="white" stroke="#10B981" stroke-width="2"/>
  <text x="1070" y="555" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #047857;">上下文</text>
  
  <!-- 连接线 -->
  <g stroke="#10B981" stroke-width="1" opacity="0.6">
    <line x1="1000" y1="453" x2="1100" y2="320"/>
    <line x1="1000" y1="453" x2="1130" y2="400"/>
    <line x1="1000" y1="453" x2="1130" y2="500"/>
    <line x1="1000" y1="453" x2="1070" y2="550"/>
  </g>
  
  <!-- 3. 领域专业能力 -->
  <circle cx="600" cy="453" r="70" fill="url(#domainGrad)" 
          stroke="#6D28D9" stroke-width="3" filter="url(#shadow)"/>
  <text x="600" y="448" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: white;">
    领域专业
  </text>
  <text x="600" y="463" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 10px; fill: #EDE9FE;">
    Domain
  </text>
  
  <!-- 领域专业子能力 -->
  <circle cx="500" cy="320" r="25" fill="white" stroke="#8B5CF6" stroke-width="2"/>
  <text x="500" y="325" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #6D28D9;">行业</text>
  
  <circle cx="470" cy="400" r="25" fill="white" stroke="#8B5CF6" stroke-width="2"/>
  <text x="470" y="405" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #6D28D9;">业务</text>
  
  <circle cx="470" cy="500" r="25" fill="white" stroke="#8B5CF6" stroke-width="2"/>
  <text x="470" y="505" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #6D28D9;">用户</text>
  
  <circle cx="530" cy="550" r="25" fill="white" stroke="#8B5CF6" stroke-width="2"/>
  <text x="530" y="555" text-anchor="middle" 
        style="font-family: Arial, sans-serif; font-size: 8px; fill: #6D28D9;">场景</text>
  
  <!-- 连接线 -->
  <g stroke="#8B5CF6" stroke-width="1" opacity="0.6">
    <line x1="600" y1="453" x2="500" y2="320"/>
    <line x1="600" y1="453" x2="470" y2="400"/>
    <line x1="600" y1="453" x2="470" y2="500"/>
    <line x1="600" y1="453" x2="530" y2="550"/>
  </g>
