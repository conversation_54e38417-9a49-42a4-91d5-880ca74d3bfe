<svg xmlns="http://www.w3.org/2000/svg" 
     viewBox="0 0 1400 1000" 
     width="1400" height="1000"
     style="max-width: 100%; height: auto; background: #f8fafc;">
  
  <!-- 样式定义区域 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="userLayerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.1" />
    </linearGradient>
    
    <linearGradient id="promptLayerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:0.1" />
    </linearGradient>
    
    <linearGradient id="modelLayerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:0.1" />
    </linearGradient>
    
    <linearGradient id="dataLayerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:0.1" />
    </linearGradient>
    
    <!-- 组件渐变 -->
    <linearGradient id="componentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F1F5F9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记定义 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 10 3.5, 0 7" fill="#475569" />
    </marker>
    
    <marker id="dataArrow" markerWidth="12" markerHeight="8" 
            refX="11" refY="4" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 4, 0 8" fill="#1E40AF" />
    </marker>
    
    <!-- 阴影滤镜定义 -->
    <filter id="dropshadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- 背景层 -->
  <rect width="100%" height="100%" fill="#f8fafc" />
  
  <!-- 主标题 -->
  <text x="700" y="40" text-anchor="middle" 
        style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #1E293B;">
    复杂Prompt工程系统架构图
  </text>
  
  <text x="700" y="65" text-anchor="middle" 
        style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #64748B;">
    Complex Prompt Engineering System Architecture
  </text>
  
  <!-- 层级容器背景 -->
  <!-- 用户交互层背景 -->
  <rect x="50" y="100" width="1300" height="120" rx="10" ry="10" 
        fill="url(#userLayerGradient)" stroke="#10B981" stroke-width="2" 
        stroke-dasharray="5,5" filter="url(#dropshadow)"/>
  <text x="70" y="125" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: #059669;">
    用户交互层 (User Interface Layer)
  </text>
  
  <!-- Prompt处理层背景 -->
  <rect x="50" y="260" width="1300" height="120" rx="10" ry="10" 
        fill="url(#promptLayerGradient)" stroke="#3B82F6" stroke-width="2" 
        stroke-dasharray="5,5" filter="url(#dropshadow)"/>
  <text x="70" y="285" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: #1E40AF;">
    Prompt处理层 (Prompt Processing Layer)
  </text>
  
  <!-- AI模型层背景 -->
  <rect x="50" y="420" width="1300" height="120" rx="10" ry="10" 
        fill="url(#modelLayerGradient)" stroke="#8B5CF6" stroke-width="2" 
        stroke-dasharray="5,5" filter="url(#dropshadow)"/>
  <text x="70" y="445" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: #7C3AED;">
    AI模型层 (AI Model Layer)
  </text>
  
  <!-- 数据存储层背景 -->
  <rect x="50" y="580" width="1300" height="120" rx="10" ry="10" 
        fill="url(#dataLayerGradient)" stroke="#F59E0B" stroke-width="2" 
        stroke-dasharray="5,5" filter="url(#dropshadow)"/>
  <text x="70" y="605" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: #D97706;">
    数据存储层 (Data Storage Layer)
  </text>
  
  <!-- 用户交互层组件 -->
  <g id="user-layer">
    <!-- 需求输入 -->
    <rect x="150" y="140" width="180" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#10B981" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="240" y="165" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">需求输入接口</text>
    <text x="240" y="185" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Requirement Input</text>
    
    <!-- 结果展示 -->
    <rect x="450" y="140" width="180" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#10B981" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="540" y="165" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">结果展示界面</text>
    <text x="540" y="185" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Result Display</text>
    
    <!-- 反馈收集 -->
    <rect x="750" y="140" width="180" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#10B981" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="840" y="165" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">反馈收集系统</text>
    <text x="840" y="185" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Feedback Collection</text>
    
    <!-- 质量监控 -->
    <rect x="1050" y="140" width="180" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#10B981" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1140" y="165" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">质量监控面板</text>
    <text x="1140" y="185" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Quality Monitor</text>
  </g>
  
  <!-- Prompt处理层组件 -->
  <g id="prompt-layer">
    <!-- 需求分析引擎 -->
    <rect x="120" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="205" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">需求分析引擎</text>
    <text x="205" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Analysis Engine</text>
    
    <!-- Prompt模板库 -->
    <rect x="320" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="405" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">Prompt模板库</text>
    <text x="405" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Template Library</text>
    
    <!-- 结构化设计器 -->
    <rect x="520" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="605" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">结构化设计器</text>
    <text x="605" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Structure Designer</text>
    
    <!-- 参数优化器 -->
    <rect x="720" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="805" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">参数优化器</text>
    <text x="805" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Parameter Optimizer</text>
    
    <!-- 上下文管理器 -->
    <rect x="920" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1005" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">上下文管理器</text>
    <text x="1005" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Context Manager</text>
    
    <!-- 质量评估器 -->
    <rect x="1120" y="300" width="170" height="70" rx="8" ry="8" 
          fill="url(#componentGradient)" stroke="#3B82F6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1205" y="325" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">质量评估器</text>
    <text x="1205" y="345" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Quality Evaluator</text>
  </g>

  <!-- AI模型层组件 -->
  <g id="model-layer">
    <!-- 模型选择器 -->
    <rect x="150" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="235" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">模型选择器</text>
    <text x="235" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Model Selector</text>

    <!-- API调用管理 -->
    <rect x="350" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="435" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">API调用管理</text>
    <text x="435" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">API Manager</text>

    <!-- 参数配置 -->
    <rect x="550" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="635" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">参数配置中心</text>
    <text x="635" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Config Center</text>

    <!-- 结果处理器 -->
    <rect x="750" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="835" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">结果处理器</text>
    <text x="835" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Result Processor</text>

    <!-- 性能监控 -->
    <rect x="950" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1035" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">性能监控器</text>
    <text x="1035" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Performance Monitor</text>

    <!-- 错误处理器 -->
    <rect x="1150" y="460" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#8B5CF6" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1235" y="485" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">错误处理器</text>
    <text x="1235" y="505" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Error Handler</text>
  </g>

  <!-- 数据存储层组件 -->
  <g id="data-layer">
    <!-- 模板数据库 -->
    <rect x="150" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="235" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">模板数据库</text>
    <text x="235" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Template DB</text>

    <!-- 历史记录 -->
    <rect x="350" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="435" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">历史记录库</text>
    <text x="435" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">History Storage</text>

    <!-- 性能指标 -->
    <rect x="550" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="635" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">性能指标库</text>
    <text x="635" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Metrics Storage</text>

    <!-- 知识库 -->
    <rect x="750" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="835" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">知识库系统</text>
    <text x="835" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Knowledge Base</text>

    <!-- 缓存系统 -->
    <rect x="950" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1035" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">缓存系统</text>
    <text x="1035" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Cache System</text>

    <!-- 配置中心 -->
    <rect x="1150" y="620" width="170" height="70" rx="8" ry="8"
          fill="url(#componentGradient)" stroke="#F59E0B" stroke-width="2" filter="url(#dropshadow)"/>
    <text x="1235" y="645" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: #1E293B;">配置中心</text>
    <text x="1235" y="665" text-anchor="middle" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #64748B;">Config Storage</text>
  </g>

  <!-- 连接线和数据流 -->
  <g id="connections">
    <!-- 主要数据流：用户输入 → Prompt处理 -->
    <line x1="240" y1="210" x2="205" y2="300" stroke="#1E40AF" stroke-width="3" marker-end="url(#dataArrow)"/>
    <text x="220" y="250" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 10px; fill: #1E40AF; font-weight: bold;">需求流</text>

    <!-- Prompt处理层内部连接 -->
    <line x1="290" y1="335" x2="320" y2="335" stroke="#3B82F6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="490" y1="335" x2="520" y2="335" stroke="#3B82F6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="690" y1="335" x2="720" y2="335" stroke="#3B82F6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="890" y1="335" x2="920" y2="335" stroke="#3B82F6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1090" y1="335" x2="1120" y2="335" stroke="#3B82F6" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- Prompt处理 → AI模型调用 -->
    <line x1="405" y1="370" x2="435" y2="460" stroke="#1E40AF" stroke-width="3" marker-end="url(#dataArrow)"/>
    <text x="415" y="420" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 10px; fill: #1E40AF; font-weight: bold;">Prompt</text>

    <!-- AI模型层内部连接 -->
    <line x1="320" y1="495" x2="350" y2="495" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="520" y1="495" x2="550" y2="495" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="720" y1="495" x2="750" y2="495" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="920" y1="495" x2="950" y2="495" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1120" y1="495" x2="1150" y2="495" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- AI模型 → 结果处理 -->
    <line x1="835" y1="530" x2="540" y2="140" stroke="#1E40AF" stroke-width="3" marker-end="url(#dataArrow)"/>
    <text x="680" y="330" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 10px; fill: #1E40AF; font-weight: bold;">结果流</text>

    <!-- 数据存储连接 -->
    <line x1="235" y1="530" x2="235" y2="620" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="405" y1="370" x2="435" y2="620" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1035" y1="530" x2="635" y2="620" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1205" y1="370" x2="835" y2="620" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- 反馈循环 -->
    <path d="M 840 210 Q 1000 250 1140 210 Q 1200 180 1205 300"
          stroke="#10B981" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
    <text x="1050" y="240" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 10px; fill: #10B981; font-weight: bold;">反馈优化</text>

    <!-- 缓存访问连接 -->
    <line x1="1005" y1="370" x2="1035" y2="620" stroke="#F59E0B" stroke-width="1.5" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
    <line x1="1235" y1="620" x2="635" y2="460" stroke="#F59E0B" stroke-width="1.5" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  </g>

  <!-- 侧边说明区域 -->
  <g id="legend">
    <!-- 图例背景 -->
    <rect x="50" y="750" width="1300" height="180" rx="10" ry="10"
          fill="#FFFFFF" stroke="#E2E8F0" stroke-width="2" filter="url(#dropshadow)"/>

    <!-- 图例标题 -->
    <text x="70" y="775" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: #1E293B;">
      系统说明 (System Description)
    </text>

    <!-- 数据流说明 -->
    <text x="70" y="800" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      • 需求流：用户输入需求，经过分析处理生成结构化Prompt
    </text>
    <text x="70" y="820" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      • Prompt流：优化后的Prompt发送给AI模型进行处理
    </text>
    <text x="70" y="840" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      • 结果流：AI模型输出结果，经过处理后返回给用户
    </text>
    <text x="70" y="860" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      • 反馈循环：基于用户反馈和质量评估持续优化系统
    </text>

    <!-- 技术特性说明 -->
    <text x="700" y="800" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      核心特性：
    </text>
    <text x="700" y="820" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      ✓ 智能需求分析和Prompt结构化设计
    </text>
    <text x="700" y="840" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      ✓ 多模型支持和参数自动优化
    </text>
    <text x="700" y="860" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      ✓ 实时性能监控和质量评估
    </text>
    <text x="700" y="880" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #475569;">
      ✓ 知识库积累和经验学习机制
    </text>

    <!-- 连接线图例 -->
    <g id="line-legend">
      <text x="70" y="900" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 12px; font-weight: bold; fill: #1E293B;">
        连接线说明：
      </text>

      <line x1="200" y1="895" x2="250" y2="895" stroke="#1E40AF" stroke-width="3" marker-end="url(#dataArrow)"/>
      <text x="260" y="900" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 11px; fill: #475569;">主要数据流</text>

      <line x1="350" y1="895" x2="400" y2="895" stroke="#475569" stroke-width="2" marker-end="url(#arrowhead)"/>
      <text x="410" y="900" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 11px; fill: #475569;">处理流程</text>

      <line x1="500" y1="895" x2="550" y2="895" stroke="#10B981" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
      <text x="560" y="900" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 11px; fill: #475569;">反馈循环</text>

      <line x1="650" y1="895" x2="700" y2="895" stroke="#F59E0B" stroke-width="1.5" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
      <text x="710" y="900" style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 11px; fill: #475569;">数据访问</text>
    </g>
  </g>

  <!-- 版权信息 -->
  <text x="700" y="980" text-anchor="middle"
        style="font-family: Arial, 'Microsoft YaHei', sans-serif; font-size: 10px; fill: #94A3B8;">
    Generated by Advanced Prompt Engineering Meta-System | 基于元提示词系统生成
  </text>

</svg>
