<svg xmlns="http://www.w3.org/2000/svg" 
     viewBox="0 0 1400 1000" 
     width="1400" height="1000"
     style="max-width: 100%; height: auto; font-family: <PERSON><PERSON>, 'Microsoft YaHei', sans-serif;">
  
  <!-- 样式定义区域 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="dangerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记定义 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1E40AF" />
    </marker>
    
    <!-- 阴影滤镜定义 -->
    <filter id="dropshadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景层 -->
  <rect width="100%" height="100%" fill="#F8FAFC" />
  
  <!-- 主标题 -->
  <text x="700" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#1E293B">
    中国电商市场竞争策略分析 - 中小卖家生存指南
  </text>
  
  <!-- 区域分割线 -->
  <line x1="675" y1="50" x2="675" y2="420" stroke="#E2E8F0" stroke-width="2"/>
  <line x1="50" y1="440" x2="1350" y2="440" stroke="#E2E8F0" stroke-width="2"/>
  <line x1="50" y1="780" x2="1350" y2="780" stroke="#E2E8F0" stroke-width="2"/>
  
  <!-- 区域1：电商平台竞争态势分析 -->
  <g id="competition-analysis">
    <!-- 区域背景 -->
    <rect x="50" y="50" width="600" height="370" fill="#FFFFFF" stroke="#E2E8F0" rx="8" filter="url(#dropshadow)"/>
    
    <!-- 区域标题 -->
    <text x="350" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="#1E293B">
      电商平台竞争态势矩阵
    </text>
    
    <!-- 坐标轴 -->
    <line x1="100" y1="380" x2="600" y2="380" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="100" y1="380" x2="100" y2="120" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- 坐标轴标签 -->
    <text x="350" y="405" text-anchor="middle" font-size="12" fill="#64748B">市场份额</text>
    <text x="70" y="250" text-anchor="middle" font-size="12" fill="#64748B" transform="rotate(-90, 70, 250)">中小卖家友好度</text>
    
    <!-- 象限标签 -->
    <text x="200" y="140" text-anchor="middle" font-size="11" fill="#64748B" opacity="0.7">高友好度/低份额</text>
    <text x="500" y="140" text-anchor="middle" font-size="11" fill="#64748B" opacity="0.7">高友好度/高份额</text>
    <text x="200" y="360" text-anchor="middle" font-size="11" fill="#64748B" opacity="0.7">低友好度/低份额</text>
    <text x="500" y="360" text-anchor="middle" font-size="11" fill="#64748B" opacity="0.7">低友好度/高份额</text>
    
    <!-- 平台气泡 -->
    <!-- 淘宝 -->
    <circle cx="520" cy="200" r="35" fill="url(#primaryGradient)" opacity="0.8"/>
    <text x="520" y="205" text-anchor="middle" font-size="11" font-weight="bold" fill="white">淘宝</text>
    
    <!-- 拼多多 -->
    <circle cx="450" cy="160" r="30" fill="url(#successGradient)" opacity="0.8"/>
    <text x="450" y="165" text-anchor="middle" font-size="10" font-weight="bold" fill="white">拼多多</text>
    
    <!-- 抖音电商 -->
    <circle cx="380" cy="180" r="25" fill="url(#warningGradient)" opacity="0.8"/>
    <text x="380" y="185" text-anchor="middle" font-size="9" font-weight="bold" fill="white">抖音</text>
    
    <!-- 天猫 -->
    <circle cx="480" cy="280" r="32" fill="url(#dangerGradient)" opacity="0.8"/>
    <text x="480" y="285" text-anchor="middle" font-size="10" font-weight="bold" fill="white">天猫</text>
    
    <!-- 京东 -->
    <circle cx="420" cy="320" r="28" fill="#6366F1" opacity="0.8"/>
    <text x="420" y="325" text-anchor="middle" font-size="9" font-weight="bold" fill="white">京东</text>
    
    <!-- 小红书 -->
    <circle cx="200" cy="170" r="20" fill="#EC4899" opacity="0.8"/>
    <text x="200" y="175" text-anchor="middle" font-size="8" font-weight="bold" fill="white">小红书</text>
    
    <!-- 快手 -->
    <circle cx="250" cy="190" r="18" fill="#8B5CF6" opacity="0.8"/>
    <text x="250" y="195" text-anchor="middle" font-size="8" font-weight="bold" fill="white">快手</text>
  </g>
  
  <!-- 区域2：中小卖家核心挑战 -->
  <g id="challenges">
    <!-- 区域背景 -->
    <rect x="700" y="50" width="650" height="370" fill="#FFFFFF" stroke="#E2E8F0" rx="8" filter="url(#dropshadow)"/>
    
    <!-- 区域标题 -->
    <text x="1025" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="#1E293B">
      中小卖家核心挑战识别
    </text>
    
    <!-- 挑战分类 -->
    <!-- 流量获取挑战 -->
    <rect x="720" y="100" width="280" height="60" fill="url(#dangerGradient)" rx="8" opacity="0.9"/>
    <text x="860" y="120" text-anchor="middle" font-size="14" font-weight="bold" fill="white">流量获取挑战</text>
    <text x="860" y="140" text-anchor="middle" font-size="11" fill="white">获客成本高 | 竞争激烈</text>
    <text x="860" y="155" text-anchor="middle" font-size="11" fill="white">流量分散 | 转化率低</text>
    
    <!-- 运营效率挑战 -->
    <rect x="1020" y="100" width="280" height="60" fill="url(#warningGradient)" rx="8" opacity="0.9"/>
    <text x="1160" y="120" text-anchor="middle" font-size="14" font-weight="bold" fill="white">运营效率挑战</text>
    <text x="1160" y="140" text-anchor="middle" font-size="11" fill="white">库存管理 | 物流成本</text>
    <text x="1160" y="155" text-anchor="middle" font-size="11" fill="white">人效低下 | 系统落后</text>
    
    <!-- 资金压力挑战 -->
    <rect x="720" y="180" width="280" height="60" fill="url(#primaryGradient)" rx="8" opacity="0.9"/>
    <text x="860" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="white">资金压力挑战</text>
    <text x="860" y="220" text-anchor="middle" font-size="11" fill="white">现金流紧张 | 资金周转</text>
    <text x="860" y="235" text-anchor="middle" font-size="11" fill="white">融资困难 | 成本上升</text>
    
    <!-- 技术能力挑战 -->
    <rect x="1020" y="180" width="280" height="60" fill="url(#successGradient)" rx="8" opacity="0.9"/>
    <text x="1160" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="white">技术能力挑战</text>
    <text x="1160" y="220" text-anchor="middle" font-size="11" fill="white">数字化程度低 | 数据分析</text>
    <text x="1160" y="235" text-anchor="middle" font-size="11" fill="white">技术人才 | 系统集成</text>
    
    <!-- 挑战关联线 -->
    <path d="M 860 260 Q 1025 280 1160 260" stroke="#64748B" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 1000 130 Q 1010 150 1020 130" stroke="#64748B" stroke-width="2" fill="none" opacity="0.6"/>
    
    <!-- 影响程度指标 -->
    <text x="1025" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="#64748B">
      挑战影响程度评估
    </text>
    
    <!-- 影响程度条形图 -->
    <rect x="750" y="320" width="200" height="15" fill="#EF4444" rx="7"/>
    <text x="650" y="332" font-size="10" fill="#64748B">流量获取</text>
    <text x="960" y="332" font-size="10" fill="#64748B">95%</text>
    
    <rect x="750" y="345" width="160" height="15" fill="#F59E0B" rx="7"/>
    <text x="650" y="357" font-size="10" fill="#64748B">运营效率</text>
    <text x="960" y="357" font-size="10" fill="#64748B">80%</text>
    
    <rect x="750" y="370" width="140" height="15" fill="#2563EB" rx="7"/>
    <text x="650" y="382" font-size="10" fill="#64748B">资金压力</text>
    <text x="960" y="382" font-size="10" fill="#64748B">70%</text>
    
    <rect x="750" y="395" width="120" height="15" fill="#059669" rx="7"/>
    <text x="650" y="407" font-size="10" fill="#64748B">技术能力</text>
    <text x="960" y="407" font-size="10" fill="#64748B">60%</text>
  </g>

  <!-- 区域3：第一性原理策略框架 -->
  <g id="strategy-framework">
    <!-- 区域背景 -->
    <rect x="100" y="460" width="1200" height="300" fill="#FFFFFF" stroke="#E2E8F0" rx="8" filter="url(#dropshadow)"/>

    <!-- 区域标题 -->
    <text x="700" y="490" text-anchor="middle" font-size="18" font-weight="bold" fill="#1E293B">
      基于第一性原理的竞争策略框架
    </text>

    <!-- 中心核心理念 -->
    <circle cx="700" cy="600" r="60" fill="url(#primaryGradient)" filter="url(#dropshadow)"/>
    <text x="700" y="595" text-anchor="middle" font-size="14" font-weight="bold" fill="white">用户价值</text>
    <text x="700" y="610" text-anchor="middle" font-size="14" font-weight="bold" fill="white">创造</text>

    <!-- 一级策略分支 -->
    <!-- 产品差异化 -->
    <ellipse cx="500" cy="520" rx="80" ry="35" fill="url(#successGradient)" opacity="0.9"/>
    <text x="500" y="520" text-anchor="middle" font-size="12" font-weight="bold" fill="white">产品差异化</text>
    <text x="500" y="535" text-anchor="middle" font-size="10" fill="white">独特价值主张</text>

    <!-- 成本优化 -->
    <ellipse cx="900" cy="520" rx="80" ry="35" fill="url(#warningGradient)" opacity="0.9"/>
    <text x="900" y="520" text-anchor="middle" font-size="12" font-weight="bold" fill="white">成本优化</text>
    <text x="900" y="535" text-anchor="middle" font-size="10" fill="white">效率提升</text>

    <!-- 渠道创新 -->
    <ellipse cx="500" cy="680" rx="80" ry="35" fill="url(#dangerGradient)" opacity="0.9"/>
    <text x="500" y="680" text-anchor="middle" font-size="12" font-weight="bold" fill="white">渠道创新</text>
    <text x="500" y="695" text-anchor="middle" font-size="10" fill="white">多元化布局</text>

    <!-- 服务升级 -->
    <ellipse cx="900" cy="680" rx="80" ry="35" fill="#6366F1" opacity="0.9"/>
    <text x="900" y="680" text-anchor="middle" font-size="12" font-weight="bold" fill="white">服务升级</text>
    <text x="900" y="695" text-anchor="middle" font-size="10" fill="white">体验优化</text>

    <!-- 连接线 -->
    <line x1="640" y1="570" x2="580" y2="540" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="760" y1="570" x2="820" y2="540" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="640" y1="630" x2="580" y2="660" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="760" y1="630" x2="820" y2="660" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- 二级策略要点 -->
    <!-- 产品差异化子策略 -->
    <rect x="300" y="480" width="120" height="25" fill="#E0F2FE" stroke="#0891B2" rx="12"/>
    <text x="360" y="497" text-anchor="middle" font-size="9" fill="#0891B2">细分市场定位</text>

    <rect x="300" y="510" width="120" height="25" fill="#E0F2FE" stroke="#0891B2" rx="12"/>
    <text x="360" y="527" text-anchor="middle" font-size="9" fill="#0891B2">产品创新研发</text>

    <rect x="300" y="540" width="120" height="25" fill="#E0F2FE" stroke="#0891B2" rx="12"/>
    <text x="360" y="557" text-anchor="middle" font-size="9" fill="#0891B2">品牌故事打造</text>

    <!-- 成本优化子策略 -->
    <rect x="980" y="480" width="120" height="25" fill="#FEF3C7" stroke="#D97706" rx="12"/>
    <text x="1040" y="497" text-anchor="middle" font-size="9" fill="#D97706">供应链优化</text>

    <rect x="980" y="510" width="120" height="25" fill="#FEF3C7" stroke="#D97706" rx="12"/>
    <text x="1040" y="527" text-anchor="middle" font-size="9" fill="#D97706">自动化运营</text>

    <rect x="980" y="540" width="120" height="25" fill="#FEF3C7" stroke="#D97706" rx="12"/>
    <text x="1040" y="557" text-anchor="middle" font-size="9" fill="#D97706">精准营销</text>

    <!-- 渠道创新子策略 -->
    <rect x="300" y="640" width="120" height="25" fill="#FEE2E2" stroke="#DC2626" rx="12"/>
    <text x="360" y="657" text-anchor="middle" font-size="9" fill="#DC2626">私域流量</text>

    <rect x="300" y="670" width="120" height="25" fill="#FEE2E2" stroke="#DC2626" rx="12"/>
    <text x="360" y="687" text-anchor="middle" font-size="9" fill="#DC2626">内容营销</text>

    <rect x="300" y="700" width="120" height="25" fill="#FEE2E2" stroke="#DC2626" rx="12"/>
    <text x="360" y="717" text-anchor="middle" font-size="9" fill="#DC2626">社交电商</text>

    <!-- 服务升级子策略 -->
    <rect x="980" y="640" width="120" height="25" fill="#EDE9FE" stroke="#7C3AED" rx="12"/>
    <text x="1040" y="657" text-anchor="middle" font-size="9" fill="#7C3AED">客户服务</text>

    <rect x="980" y="670" width="120" height="25" fill="#EDE9FE" stroke="#7C3AED" rx="12"/>
    <text x="1040" y="687" text-anchor="middle" font-size="9" fill="#7C3AED">物流体验</text>

    <rect x="980" y="700" width="120" height="25" fill="#EDE9FE" stroke="#7C3AED" rx="12"/>
    <text x="1040" y="717" text-anchor="middle" font-size="9" fill="#7C3AED">售后保障</text>

    <!-- 连接线到子策略 -->
    <path d="M 420 520 Q 460 520 500 520" stroke="#0891B2" stroke-width="1" fill="none" opacity="0.6"/>
    <path d="M 580 520 Q 620 520 660 520" stroke="#D97706" stroke-width="1" fill="none" opacity="0.6"/>
    <path d="M 420 680 Q 460 680 500 680" stroke="#DC2626" stroke-width="1" fill="none" opacity="0.6"/>
    <path d="M 580 680 Q 620 680 660 680" stroke="#7C3AED" stroke-width="1" fill="none" opacity="0.6"/>
  </g>

  <!-- 区域4：具体实施路径 -->
  <g id="implementation-path">
    <!-- 区域背景 -->
    <rect x="50" y="800" width="1300" height="150" fill="#FFFFFF" stroke="#E2E8F0" rx="8" filter="url(#dropshadow)"/>

    <!-- 区域标题 -->
    <text x="700" y="830" text-anchor="middle" font-size="18" font-weight="bold" fill="#1E293B">
      分阶段实施路径与盈利模式
    </text>

    <!-- 时间轴 -->
    <line x1="100" y1="900" x2="1300" y2="900" stroke="#64748B" stroke-width="3"/>

    <!-- 短期阶段 (1-3个月) -->
    <circle cx="250" cy="900" r="8" fill="url(#successGradient)"/>
    <rect x="150" y="850" width="200" height="40" fill="url(#successGradient)" rx="8" opacity="0.9"/>
    <text x="250" y="865" text-anchor="middle" font-size="12" font-weight="bold" fill="white">短期 (1-3个月)</text>
    <text x="250" y="880" text-anchor="middle" font-size="10" fill="white">基础建设 | 流量获取</text>

    <text x="250" y="920" text-anchor="middle" font-size="10" fill="#64748B">• 选品策略优化</text>
    <text x="250" y="935" text-anchor="middle" font-size="10" fill="#64748B">• 店铺装修升级</text>

    <!-- 中期阶段 (3-12个月) -->
    <circle cx="600" cy="900" r="8" fill="url(#warningGradient)"/>
    <rect x="500" y="850" width="200" height="40" fill="url(#warningGradient)" rx="8" opacity="0.9"/>
    <text x="600" y="865" text-anchor="middle" font-size="12" font-weight="bold" fill="white">中期 (3-12个月)</text>
    <text x="600" y="880" text-anchor="middle" font-size="10" fill="white">规模扩张 | 效率提升</text>

    <text x="600" y="920" text-anchor="middle" font-size="10" fill="#64748B">• 多平台布局</text>
    <text x="600" y="935" text-anchor="middle" font-size="10" fill="#64748B">• 供应链整合</text>

    <!-- 长期阶段 (1-3年) -->
    <circle cx="950" cy="900" r="8" fill="url(#primaryGradient)"/>
    <rect x="850" y="850" width="200" height="40" fill="url(#primaryGradient)" rx="8" opacity="0.9"/>
    <text x="950" y="865" text-anchor="middle" font-size="12" font-weight="bold" fill="white">长期 (1-3年)</text>
    <text x="950" y="880" text-anchor="middle" font-size="10" fill="white">品牌建设 | 生态构建</text>

    <text x="950" y="920" text-anchor="middle" font-size="10" fill="#64748B">• 品牌价值提升</text>
    <text x="950" y="935" text-anchor="middle" font-size="10" fill="#64748B">• 生态系统构建</text>

    <!-- 连接箭头 -->
    <path d="M 350 900 L 500 900" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M 700 900 L 850 900" stroke="#64748B" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- 盈利预期指标 -->
    <text x="1150" y="870" text-anchor="middle" font-size="12" font-weight="bold" fill="#64748B">预期ROI</text>
    <text x="1150" y="890" text-anchor="middle" font-size="14" font-weight="bold" fill="#059669">15-25%</text>
    <text x="1150" y="910" text-anchor="middle" font-size="14" font-weight="bold" fill="#F59E0B">25-40%</text>
    <text x="1150" y="930" text-anchor="middle" font-size="14" font-weight="bold" fill="#2563EB">40-60%</text>
  </g>

</svg>
